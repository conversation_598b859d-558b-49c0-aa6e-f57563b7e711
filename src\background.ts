export {}

import { getBackgroundText } from './i18n'
import { getUserSettings } from './utils/storage'

// 创建右键菜单项
chrome.runtime.onInstalled.addListener(async () => {
  await createContextMenu()
})

// 创建或更新右键菜单
async function createContextMenu() {
  try {
    // 获取用户设置的格式
    const settings = await getUserSettings()
    const format = settings.outputFormat || 'JPG'

    // 移除现有菜单项
    chrome.contextMenus.removeAll(() => {
      // 创建新的菜单项
      const menuTitle = getBackgroundText('contextMenu.saveAsFormat').replace('{{format}}', format)

      chrome.contextMenus.create({
        id: "saveImageAsFormat",
        title: menuTitle,
        contexts: ["image"]
      })

      console.log('右键菜单已更新:', menuTitle)
    })
  } catch (error) {
    console.error('创建右键菜单失败:', error)
    // 回退到默认菜单
    chrome.contextMenus.create({
      id: "saveImageAsFormat",
      title: getBackgroundText('contextMenu.saveAsFormat').replace('{{format}}', 'JPG'),
      contexts: ["image"]
    })
  }
}

// 监听来自 popup 的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.action === "updateContextMenu") {
    createContextMenu()
    sendResponse({ success: true })
  }
})

// 处理右键菜单点击事件
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  if (info.menuItemId === "saveImageAsFormat" && info.srcUrl && tab?.id) {
    await downloadImageAsFormat(info.srcUrl, tab.id)
  }
})

// 下载图片并转换为指定格式
async function downloadImageAsFormat(imageUrl: string, tabId: number) {
  try {
    // 获取用户设置的格式
    const settings = await getUserSettings()
    const targetFormat = settings.outputFormat || 'JPG'

    // 向 content script 发送消息进行图片转换
    const response = await chrome.tabs.sendMessage(tabId, {
      action: "convertImageToFormat",
      imageUrl: imageUrl,
      targetFormat: targetFormat
    })

    if (response.success) {
      // 转换成功，使用传统方法下载转换后的图片
      console.log('🎯 准备下载转换后的图片:', {
        fileName: response.fileName,
        dataUrlLength: response.dataUrl.length,
        targetFormat: targetFormat
      })

      // 优先使用content script的传统下载方法
      await downloadViaContentScript(tabId, response.dataUrl, response.fileName, targetFormat)

    } else {
      // 转换失败
      console.warn('❌ 图片转换失败:', response.error)
    }

  } catch (error) {
    console.error('❌ 下载图片失败:', error)
    console.error('   图片URL:', imageUrl)
    console.error('   标签ID:', tabId)
  }
}

// 通过content script使用传统DOM方法下载
async function downloadViaContentScript(tabId: number, dataUrl: string, fileName: string, targetFormat: string): Promise<void> {
  try {
    console.log('📥 使用content script传统下载方法')
    
    // 发送消息给content script，让它使用传统DOM下载
    const response = await chrome.tabs.sendMessage(tabId, {
      action: "downloadWithDOM",
      dataUrl: dataUrl,
      fileName: fileName
    })
    
    if (response && response.success) {
      console.log(`✅ ${targetFormat} 图片通过DOM下载成功:`, fileName)
    } else {
      console.warn('⚠️ content script下载失败')
    }
  } catch (error) {
    console.error('❌ content script下载失败:', error)
  }
}

console.log('图片下载扩展后台脚本已加载')
